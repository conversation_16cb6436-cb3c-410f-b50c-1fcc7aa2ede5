import pyodbc
import os
from dotenv import load_dotenv
from datetime import datetime, timedelta
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
from main import airtanker_app


class DatabaseService:
    '''
    This class is using the pyodbc library to connect to a SQL Server database.
    '''
    def __init__(self):
        self.connection = None


    def connect(self):
        try:
            load_dotenv()

            # Retrieve database credentials from environment variables
            db_server = os.getenv("DB_SERVER")
            db_database = os.getenv("DB_DATABASE")
            db_username = os.getenv("DB_USERNAME")
            db_password = os.getenv("DB_PASSWORD")

            if db_server is None:
                raise Exception("DB_SERVER is not set in the environment variables")
            if db_database is None:
                raise Exception("DB_DATABASE is not set in the environment variables")
            if db_username is None:
                raise Exception("DB_USERNAME is not set in the environment variables")
            if db_password is None:
                raise Exception("DB_PASSWORD is not set in the environment variables")

            # connect
            self.connection = pyodbc.connect(
                f'DRIVER={{ODBC Driver 17 for SQL Server}};'
                f'SERVER={db_server};'
                f'DATABASE={db_database};'
                f'UID={db_username};'
                f'PWD={db_password}')
            #airtanker_app.logger.info(f"Successful connection to {db_server}")
            return True
        except Exception as e:
            airtanker_app.logger.error(f"Error connecting to database: {e}")
            return False
    

    def disconnect(self):
        if self.connection:
            self.connection.close()


    def get_active_work_orders(self, selected_week_ending):
        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()

        # If the selected_week_ending is a string, convert it to a datetime object
        if isinstance(selected_week_ending, str):
            selected_week_ending = datetime.strptime(selected_week_ending, '%Y-%m-%d')

        prev_week_ending = selected_week_ending - timedelta(days=7)

        # Format the dates as strings if necessary, depending on how your database expects dates
        week_ending_str = selected_week_ending.strftime('%Y-%m-%d')
        prev_week_ending_str = prev_week_ending.strftime('%Y-%m-%d')

        get_all_active_work_order_entries_query = """
            SELECT *
            FROM [dbo].[WorkOrderFullDetails]
            WHERE 
                (
                    [StageID] = 187 AND 
                    [StartDate] <= ?
                ) 
        """        
                # OR 
                # (
                #     [StageID] <> 187 AND 
                #     ([AnticipatedEndDate] > ?) AND 
                #     [StartDate] <= ?
                # )
        # if the StartDate is less than or equal to week ending, and active
        # or it's inactive but the (EndDate is greater than previous week ending or end date is NULL) -- REMOVED Drew 12/4/24
        # and StartDate is less than or equal to week_ending
        cursor.execute(get_all_active_work_order_entries_query, (week_ending_str)) #, prev_week_ending_str, week_ending_str))
        columns = [column[0] for column in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return results


    def execute_query(self, query, parameters=None):
        if not self.connection:
            print("Not connected to a database")
            return None
        results = None
        cursor = self.connection.cursor()
        try:
            if parameters:
                cursor.execute(query,(parameters))
            else:
                cursor.execute(query)
            if cursor.description:
                columns = [column[0] for column in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            self.connection.commit()

            return results
        except Exception as e:
            airtanker_app.logger.error(f"Failed to execute query: {query}: {e}")
            return None


    def toggle_timesheet_bill_created(self, timesheet_id, write_bill_id):
        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()
        try:
            query = """
                UPDATE [dbo].[Timesheets]
                SET [Bill_Created] = ?, [StatusID] = 8
                WHERE [TimesheetID] = ?
            """
            cursor.execute(query,(write_bill_id, timesheet_id))
            self.connection.commit()
        except Exception as e:
            print(f"Error executing query: {e}")
            return None
        
    
    def toggle_timesheet_upload_created(self, timesheet_id, odoo_timesheet_id, internal=False):
        if not self.connection:
            error_msg = "Database connection not established"
            airtanker_app.logger.error(error_msg)
            raise Exception(error_msg)
        
        cursor = self.connection.cursor()
        try:
            if not internal:
                query = """
                    UPDATE [dbo].[Timesheets]
                    SET [Timesheet_Created] = ?, [StatusID] = 8
                    WHERE [TimesheetID] = ?
                """
                cursor.execute(query, (odoo_timesheet_id, timesheet_id))
            else:
                query = """
                    UPDATE [dbo].[Internal_Timesheets]
                    SET [Timesheet_Created] = ?
                    WHERE [ID] = ?
                """
                cursor.execute(query, (odoo_timesheet_id, timesheet_id))
            
            self.connection.commit()
            
        except Exception as e:
            error_msg = f"Database error updating timesheet {timesheet_id}: {str(e)}"
            airtanker_app.logger.error(error_msg)
            raise Exception(error_msg)
        finally:
            cursor.close()

    def batch_update_timesheets(self, update_data, is_internal=False):
        if not self.connection:
            print("Not connected to database")
            return None

        if is_internal:
            query = """
                UPDATE [dbo].[Internal_Timesheets]
                SET [Timesheet_Created] = ?
                WHERE [ID] = ?
            """
        else:
            query = """
                UPDATE [dbo].[Timesheets]
                SET [Timesheet_Created] = ?, [StatusID] = 8
                WHERE [TimesheetID] = ?
            """

        params = [(odoo_id, record_id) for odoo_id, record_id in update_data]
        
        try:
            with self.connection.cursor() as cursor:
                cursor.executemany(query, params)
                self.connection.commit()
                return True
        except Exception as e:
            self.connection.rollback()
            airtanker_app.logger.error(f"batch_update_timesheets failed: {str(e)}")
            return False

    def toggle_timesheet_invoice_created(self, timesheet_id, invoiceID):
        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()
        try:
            query = """
                UPDATE [dbo].[Timesheets]
                SET [Invoice_Created] = ?, [StatusID] = 8
                WHERE [TimesheetID] = ?
            """
            cursor.execute(query,(invoiceID, timesheet_id))
            self.connection.commit()
        except Exception as e:
            print(f"Error executing query: {e}")
            return None


    def find_or_add_employee(self, name, type, employee_id=None): # using HumanName class for extra
        '''Finds Employee / Contractor based on their name. 
        Adds employee to the database, based on name if not found. 
        Returns the EmployeeID.
        Returns None if connection cannot be established to DB.'''

        if not self.connection:
            print("Not connected to a database")
            return None

        cursor = self.connection.cursor()

        # Check if the employee exists
        cursor.execute("SELECT EmployeeID FROM Employees WHERE FirstName = ? AND LastName = ?", (name.first, name.last))
        row = cursor.fetchone()

        if row:
            return row[0]
        else:
            if employee_id is not None:
                cursor.execute("SELECT FirstName, LastName, FullName FROM Employees WHERE EmployeeID = ?", employee_id)
                row = cursor.fetchone()
                if row:
                    if name.last == row[1] or name.first == row[0] or name.full_name in row[2] or (row[0] in name.full_name and row[1] in name.full_name):
                        return employee_id
                    else:
                        raise Exception(f"Employee with ID {employee_id} already exists in table under the name {row[2]}. AirTanker is trying to record it as {name.full_name}")
                else:                
                    # Set identity insert on for manual EmployeeID insertion
                    cursor.execute("SET IDENTITY_INSERT Employees ON")

                    # Insert new employee with specified EmployeeID
                    cursor.execute("""
                        INSERT INTO [dbo].[Employees] ([EmployeeID], [FirstName], [MiddleName], [LastName], [FullName], [Type])
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (employee_id, name.first, name.middle, name.last, name.full_name, type))

                    cursor.execute("SET IDENTITY_INSERT Employees OFF")
            else:
                # Insert new employee without specifying EmployeeID
                cursor.execute("""
                    INSERT INTO [dbo].[Employees] ([FirstName], [MiddleName], [LastName], [FullName], [Type])
                    OUTPUT INSERTED.EmployeeID
                    VALUES (?, ?, ?, ?, ?)
                """, (name.first, name.middle, name.last, name.full_name, type))

                employee_id = cursor.fetchone()[0]

            self.connection.commit()  # Commit the transaction
            return employee_id
        

    def find_employee(self, name, type): # using HumanName class for extra
        '''Finds Employee / Contractor based on their name. 
        Returns the EmployeeID.
        Returns None if connection cannot be established to DB.'''

        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()
        # Check if the employee exists
        cursor.execute("SELECT EmployeeID FROM Employees WHERE FirstName = ? AND LastName = ?", (name.first, name.last))
        row = cursor.fetchone()
        
        if row:
            return row[0]
        else:           
            return None
        

    def get_employee_reported_hours(self, timesheet_data): # using HumanName class for extra
        '''Finds Employee / Contractor based on their name. 
        Returns a list of reported hours.
        Returns None if connection cannot be established to DB.'''

        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()

        timesheet_id = timesheet_data['TimeSheetID']
        source = timesheet_data['ApprovedHoursSource']

        approved_source = "'Internal', 'Paycor'" # Source type will be eHours if it stays Internal
        if source == "adpHours":
            approved_source = "'ADP'"
        
        query = """
            SELECT * 
            FROM vw_EmployeeHoursWithSource
            WHERE TimesheetID = ? AND SourceType IN ('Internal', 'Paycor')
            ORDER BY Date ASC
        """
        cursor.execute(query, (timesheet_id))
        
        columns = [column[0] for column in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        self.connection.commit()
        return results


    def find_or_create_timesheet(self, employee_id, date, work_order_id=None):
        '''Finds the Weekly TimeSheet record based on the 
        EmployeeID and the day they worked.

        This method parses the date to get the week_start_date.
        Week will always start on a Monday (end on Sunday).

        Returns TimeSheetID (Weekly TimeSheet)
        Returns None if connection cannot be established to DB.'''
        try:
            if not self.connection:
                print("Database connection is not established")
                return None

            # This seems to be a stub for specific logic with employee_id 290.
            if employee_id == 290:
                pass

            cursor = self.connection.cursor()
            # Calculate the starting and ending dates for the week
            week_start_date = date - timedelta(days=date.weekday())
            week_end_date = get_week_ending_date(date)

            query = """
                SELECT TimesheetID FROM Timesheets
                WHERE EmployeeID = ? AND WeekStarting = ?            
            """
            parameters = (employee_id, week_start_date)

            if work_order_id:
                query += " AND WorkOrderID = ?"
                parameters += (work_order_id,)

            cursor.execute(query, parameters)
            result = cursor.fetchone()

            if result:
                return result[0]  # Timesheet exists, return its ID
            else:
                # No matching timesheet, create a new one
                cursor.execute("""
                    INSERT INTO Timesheets (
                        EmployeeID, WeekStarting, WeekEnding,
                        WorkOrderID, StatusID, SubmissionDate
                    )
                    OUTPUT INSERTED.TimesheetID
                    VALUES (?, ?, ?, ?, 7, GETDATE())
                """, (employee_id, week_start_date, week_end_date, work_order_id))
                inserted_result = cursor.fetchone()
                if inserted_result is None:
                    airtanker_app.logger.error("Failed to insert and retrieve new timesheet ID")
                    return None
                timesheet_id = inserted_result[0]
                self.connection.commit()
                return timesheet_id  # Return the new TimesheetID
        except Exception as e:
            airtanker_app.logger.error(
                f"Error occurred in find_or_create_timesheet: {e}",
                exc_info=True
            )
            return None

    def find_or_create_project(self, project_number, project_desc="", op_code=None):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()

        if project_number is None:
            if project_desc == "":
                project_number = "Unknown"
            else:
                project_number = project_desc
        
        # Attempt to find an existing project by name for the specified customer
        cursor.execute("""
            SELECT ProjectID FROM Projects
            WHERE ProjectNumber = ?
        """, (project_number))
        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            # No existing project found, create a new one
            columns = "ProjectNumber, ProjectDescription, Status"
            placeholders = "?, ?, ?"  # Placeholders for the values
            values = [project_number, project_desc, "Active"]
            
            # Conditionally add LocationID to the columns and values
            if op_code:
                columns += ", OpCode"
                placeholders += ", ?"
                values.append(op_code)

            # Construct the full INSERT query
            sql_query = f"""
                INSERT INTO Projects ({columns}) 
                OUTPUT INSERTED.ProjectID 
                VALUES ({placeholders})
            """

            # Execute the query with the constructed values
            cursor.execute(sql_query, values)
            new_project_id = cursor.fetchone()[0]
            self.connection.commit()
            return new_project_id
        
        
    def find_or_create_customer(self, customer_name):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()

        # Attempt to find an existing customer by name
        cursor.execute("""
            SELECT CustomerID FROM Customers
            WHERE CustomerName = ?
        """, (customer_name,))
        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            # No existing customer found, create a new one
            cursor.execute("""
                INSERT INTO Customers (CustomerName) 
                OUTPUT INSERTED.CustomerID 
                VALUES (?)
            """, (customer_name,))
            new_customer_id = cursor.fetchone()[0]
            self.connection.commit()
            return new_customer_id


    def insert_hours_internal(self,
                            employee_id, 
                            date, 
                            employee_reported_hours,
                            project_id,
                            customer_id, 
                            file_id, 
                            work_order_id, 
                            task_id=None, 
                            location_id=None):
        # TODO in case someone runs the same sheet twice, this will
        # create duplicates in the data for the hours.
        # With this schema we'll only need to manage the Entries table
        '''We could check for duplicates manually with this:
        SELECT EmployeeID, ProjectID, Date, HoursWorked, COUNT(*)
        FROM TimeSheetEntries
        GROUP BY EmployeeID, ProjectID, Date, HoursWorked
        HAVING COUNT(*) > 1;'''

        '''This method inserts a DailyTimesheet record into the 
        TimesheetEntries table based on the weekly TimeSheet record.
        
        It also inserts a Weekly Timesheet record into the Timesheet
        table, using the find_or_create_timesheet method.'''



        # TODO need the TimesheetEntryID and the TimesheetID

        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()
        
        # Insert or find a Weekly record.
        timesheet_id = self.find_or_create_timesheet(employee_id=employee_id,
                                                     date=date,
                                                     work_order_id=work_order_id)

        # Insert or find a Daily record.
        timesheet_entry_id = self.find_or_create_timesheetEntry(timesheet_id=timesheet_id, date=date)
        
        # Insert into EmployeeReportedHours Table
        columns = "TimesheetEntryID, Date, ProjectID, EmployeeReportedHours, FileID, CustomerID, WorkOrderID, EmployeeID" # TaskID, LocationID can be added if passed
        placeholders = "?, ?, ?, ?, ?, ?, ?, ?"  # Placeholders for the values
        values = [timesheet_entry_id, date, project_id, employee_reported_hours, file_id, customer_id, work_order_id, employee_id]


        # Conditionally add TaskID to the columns and values
        if task_id:
            columns += ", TaskID"
            placeholders += ", ?"
            values.append(task_id)

        # Conditionally add LocationID to the columns and values
        if location_id:
            columns += ", LocationID"
            placeholders += ", ?"
            values.append(location_id)

        # Construct the full INSERT query
        sql_query = f"""
            INSERT INTO EmployeeReportedHours ({columns})
            VALUES ({placeholders})
        """

        # Execute the query with the constructed values
        cursor.execute(sql_query, values)
        self.connection.commit()
        return True


    def find_or_create_timesheetEntry(self, timesheet_id, date):
        if not self.connection:
            print("Not connected to a database")
            return None

        try:
            with self.connection.cursor() as cursor:
                # Try to fetch the existing timesheet entry.
                cursor.execute(
                    """
                    SELECT EntryID 
                    FROM TimesheetEntries
                    WHERE TimesheetID = ? AND Date = ?
                    """,
                    (timesheet_id, date)
                )
                result = cursor.fetchone()

                if result:
                    return result[0]

                # Insert new timesheet entry using the OUTPUT clause to capture the new EntryID.
                cursor.execute(
                    """
                    INSERT INTO TimesheetEntries (TimesheetID, Date)
                    OUTPUT inserted.EntryID
                    VALUES (?, ?)
                    """,
                    (timesheet_id, date)
                )
                new_entry = cursor.fetchone()
                if new_entry:
                    new_entry_id = new_entry[0]
                else:
                    # In case there's an issue with the insertion.
                    self.connection.rollback()
                    return None

            # Commit the changes once the context is done.
            self.connection.commit()
            return new_entry_id

        except Exception as e:
            # Roll back the transaction if an error occurs.
            self.connection.rollback()
            print("An error occurred:", e)
            return None


    def get_employee_hours_ids(self, timesheet_entry_id, customer_id):
        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()
        
        # Check if an entry exists for the given date and project_id
        cursor.execute("""
            SELECT EmployeeReportedHoursID FROM [EmployeeReportedHours]
            WHERE [TimesheetEntryID] = ? and CustomerID = ?
        """, (timesheet_entry_id, customer_id))
        rows = cursor.fetchall()

        if len(rows) < 1:
            return None
        else:
            return rows
    

    def insert_customer_reported_hours(self, timesheet_entry_id, project_id, customer_id, date, customer_reported_hours, employee_hours_id, file_id):
        '''Gets the TimeSheet ID using the employee Id and date.
        Inserts the record into the database for the customer.

        '''
        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()
        cursor.execute("""
                SELECT [EmployeeReportedHours] FROM [EmployeeReportedHours]
                WHERE [EmployeeReportedHoursID] = ?
            """, (employee_hours_id))
        row = cursor.fetchone()
        interal_reported_hours = row[0]

        # deduct the internal hours (sometimes theres more than one entry)
        hours = customer_reported_hours - float(interal_reported_hours)

        # Dynamically build the SQL query
        insert_query = """
                INSERT INTO CustomerReportedHours (CustomerReportedHours, ProjectID, TimesheetEntryID, CustomerID, Date, FileID)
                VALUES (?, ?, ?, ?, ?, ?)
            """
        cursor.execute(insert_query, (float(interal_reported_hours), project_id, timesheet_entry_id, customer_id, date, file_id))
        self.connection.commit()
        return hours
    
    
    def insert_customer_reported_hours_lastRow(self,
                                            employee_id, 
                                            date, 
                                            customer_reported_hours, 
                                            project_id, 
                                            customer_id, 
                                            file_id, 
                                            work_order_id, 
                                            task_id=None, 
                                            location_id=None,
                                            rate_type=None):
        '''Gets the TimeSheet ID using the employee Id and date.
        Inserts the record into the database for the customer.

        '''
        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()
        
        # Insert or find a Weekly record.
        timesheet_id = self.find_or_create_timesheet(employee_id=employee_id,
                                                     date=date,
                                                     work_order_id=work_order_id)

        # Insert or find a Daily record.
        timesheet_entry_id = self.find_or_create_timesheetEntry(timesheet_id=timesheet_id, date=date)


        # Insert into CustomerReportedHours Table
        columns = "TimesheetEntryID, Date, ProjectID, CustomerReportedHours, FileID, CustomerID, WorkOrderID, EmployeeID"
        placeholders = "?, ?, ?, ?, ?, ?, ?, ?"  # Placeholders for the values
        values = [timesheet_entry_id, date, project_id, customer_reported_hours, file_id, customer_id, work_order_id, employee_id]


        # Conditionally add TaskID to the columns and values
        if task_id:
            columns += ", TaskID"
            placeholders += ", ?"
            values.append(task_id)

        # Conditionally add LocationID to the columns and values
        if location_id:
            columns += ", LocationID"
            placeholders += ", ?"
            values.append(location_id)

        # Conditionally add RateTypeID to the columns and values
        if rate_type:
            columns += ", RateTypeID"
            placeholders += ", ?"
            values.append(rate_type)

        # Construct the full INSERT query
        sql_query = f"""
            INSERT INTO CustomerReportedHours ({columns})
            VALUES ({placeholders})
        """

        # Execute the query with the constructed values
        cursor.execute(sql_query, values)
        self.connection.commit()
        return True

    def insert_employee_reported_hours_with_type(self,
                                                 employee_id,
                                                 date,
                                                 employee_reported_hours,
                                                 project_id,
                                                 customer_id,
                                                 file_id,
                                                 work_order_id,
                                                 task_id=None,
                                                 location_id=None,
                                                 rate_type_id=None,
                                                 notes=None):
        """
        Inserts a record into the EmployeeReportedHours table, including RateTypeID, CustomerID, and TaskID.
        Handles finding or creating the necessary Timesheet and TimesheetEntry records.
        """
        if not self.connection:
            airtanker_app.logger.error("Database connection is not established for insert_employee_reported_hours_with_type.")
            # Consider raising an exception or returning a more specific error indicator
            return False

        cursor = self.connection.cursor()

        try:
            # Insert or find a Weekly record.
            timesheet_id = self.find_or_create_timesheet(employee_id=employee_id,
                                                         date=date,
                                                         work_order_id=work_order_id)
            if timesheet_id is None:
                 airtanker_app.logger.error(f"Failed to find or create Timesheet for EmployeeID {employee_id}, Date {date}, WO {work_order_id}")
                 return False # Indicate failure

            # Insert or find a Daily record.
            timesheet_entry_id = self.find_or_create_timesheetEntry(timesheet_id=timesheet_id, date=date)
            if timesheet_entry_id is None:
                airtanker_app.logger.error(f"Failed to find or create TimesheetEntry for TimesheetID {timesheet_id}, Date {date}")
                return False # Indicate failure

            # Base columns and placeholders - Ensure column names match your table exactly
            columns = "[TimesheetEntryID], [Date], [CustomerID], [ProjectID], [TaskID], [EmployeeReportedHours], [Notes], [FileID], [RateTypeID], [WorkOrderID], [EmployeeID]"
            # Added CustomerID, TaskID, RateTypeID. Assuming LocationID is nullable or handled if needed.
            # Order placeholders to match columns
            placeholders = "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?"
            values = [timesheet_entry_id, date, customer_id, project_id, task_id, employee_reported_hours, notes, file_id, rate_type_id, work_order_id, employee_id]

            if location_id:
                columns += ", LocationID"
                placeholders += ", ?"
                values.append(location_id)

            # Construct the full INSERT query
            sql_query = f"""
                INSERT INTO [dbo].[EmployeeReportedHours] ({columns})
                VALUES ({placeholders})
            """

            # Execute the query
            cursor.execute(sql_query, values)
            self.connection.commit()
            # airtanker_app.logger.debug(f"Successfully inserted reported hours for EmployeeID {employee_id}, Date {date}, WO {work_order_id}, RateTypeID {rate_type_id}")
            return True # Indicate success

        except Exception as e:
            airtanker_app.logger.error(f"Error inserting employee reported hours with type: {e}", exc_info=True)
            try:
                self.connection.rollback() # Rollback on error
            except Exception as rb_ex:
                airtanker_app.logger.error(f"Error during rollback: {rb_ex}", exc_info=True)
            return False # Indicate failure
        finally:
             if cursor:
                 cursor.close()


    def get_employee_details_daily(self, weekEnding):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()
        search_query = """
            SELECT [TimesheetID]
                ,[TimeSheetEntryID]
                ,FORMAT(Date, 'MM-dd-yyyy') as Date
                ,[FirstName]
                ,[LastName]
                ,[CustomerName]
                ,[WorkOrderNumber]
                ,[CustomerReportedHours]
                ,[EmployeeReportedHours]
                ,[ADPReportedHours]
                ,[ErrorIndicator]
                ,[Notes]
            FROM [dbo].[vw_EmployeeDetailsDaily]
            WHERE WeekEnding = ?
        """ 
        cursor.execute(search_query, weekEnding)
        columns = [column[0] for column in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return result
    

    def get_employee_details_daily_by_timesheetID(self, timesheetID):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()
        search_query = """
            SELECT [TimesheetID]
                ,[TimeSheetEntryID]
                ,FORMAT(Date, 'MM-dd-yyyy') as Date
                ,[FirstName]
                ,[LastName]
                ,[CustomerName]
                ,[WorkOrderNumber]
                ,[CustomerReportedHours]
                ,[EmployeeReportedHours]
                ,[ADPReportedHours]
                ,[ErrorIndicator]
                ,[Notes]
            FROM [dbo].[vw_EmployeeDetailsDaily]
            WHERE [TimesheetID] = ?
        """ 
        cursor.execute(search_query, timesheetID)
        columns = [column[0] for column in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return result
    

    def get_employee_details_weekly(self, weekEnding):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()
        search_query = """
            SELECT [EmployeeID]
                ,[FirstName]
                ,[LastName]
                ,[TimeSheetID]
                ,[WeekEnding]
                ,[Status]
                ,[WorkOrderID]
                ,[WorkOrderNumber]
                ,[InternalReportedHours]
                ,[ADPReportedHours]
                ,[TotalCustomerReportedHours]
                ,[ApprovedHours]
                ,[ExpensesUploaded]
            FROM [dbo].[vw_EmployeeDetailsWeekly]
            WHERE WeekEnding = ?
        """ 
        cursor.execute(search_query, weekEnding)
        columns = [column[0] for column in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return result
    

    def get_employee_details_weekly_work_ids(self, weekEnding, isBill, isInvoice, isTimesheet=False, include_expenses=False):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()

        bill_invoice_check = ""
        if isBill:
            bill_invoice_check += " and (Bill_Created = 0 OR Bill_Created is NULL)"
        if isInvoice:
            bill_invoice_check += " and (Invoice_Created = 0 OR Invoice_Created is NULL)"
        if isTimesheet:
            bill_invoice_check += " and (Timesheet_Created = 0 OR Timesheet_Created is NULL)"
        # Get all the approved or approved with errors entries
        search_query = """
            SELECT [WorkOrderID]
                ,[ApprovedHours]
                ,[WeekStarting]
                ,[WeekEnding]
                ,[ApprovedHoursSource]
                ,[TimeSheetID]
                ,[Bill_Created]
	            ,[Invoice_Created]
            FROM [dbo].[vw_EmployeeDetailsWeekly]
            WHERE [WeekEnding] = ? and ([StatusID] = 3 OR [StatusID] = 6 OR [StatusID] = 8)
        """
        search_query += bill_invoice_check

        cursor.execute(search_query, weekEnding)
        columns = [column[0] for column in cursor.description]
        approved_hours = [dict(zip(columns, row)) for row in cursor.fetchall()]

        if include_expenses:
            invoice_query = """
                SELECT *
                FROM [dbo].[vw_ReportedExpensesWithTimesheets]
                WHERE [WeekEnding] = ? and ([Expense_StatusID] = 3 OR [Expense_StatusID] = 6 OR [Expense_StatusID] = 8)
            """
            cursor.execute(invoice_query, weekEnding)
            expense_columns = [column[0] for column in cursor.description]
            expenses = [dict(zip(expense_columns, row)) for row in cursor.fetchall()]

            # Aggregate expenses by TimesheetID
            for time_record in approved_hours:
                timesheet_id = time_record['TimeSheetID']
                expense = get_first_match(expenses, "TimesheetID", timesheet_id)

                time_record['Expenses'] = expense

        return approved_hours


    def update_timesheet_status(self, timeSheetID, statusID, hours, hours_source, timesheets=True):
        if not self.connection:
            return "Datebase connection is not established."

        cursor = self.connection.cursor()
        try:
            search_query = ""
            if timesheets:
                search_query = """
                    UPDATE [dbo].[Timesheets]
                        SET StatusID = ?, ApprovedHours = ?, ApprovedHoursSource = ?
                    WHERE TimesheetID = ?
                """
                parameters = (statusID, hours, hours_source, timeSheetID)
            else:
                search_query = """
                    UPDATE [dbo].[Timesheets]
                        SET Expense_StatusID = ?, [ApprovedTotalExpenses] = ?
                    WHERE TimesheetID = ?
                """
                parameters = (statusID, hours, timeSheetID)
            cursor.execute(search_query, parameters)
            self.connection.commit()  # Don't forget to commit the transaction
            return None
        except Exception as e:
            return e
        

    def insert_audit_record(self, timeSheetID, statusID, username, message, type="Timesheets"):
        if not self.connection:
            print("Not connected to a database")
            return None
        
        cursor = self.connection.cursor()

        
        # Insert into TimeSheetEntries Table
        columns = """[TimesheetID]
                ,[ApprovalStatus]
                ,[ReviewedBy]
                ,[ReviewDate]
                ,[Notes]
                ,[ApprovalType]
        """
        placeholders = "?, ?, ?, GETDATE(), ?, ?"  # Placeholders for the values
        values = [timeSheetID, statusID, username, message, type]

        # Construct the full INSERT query
        sql_query = f"""
            INSERT INTO [dbo].[ApprovalTracking] ({columns})
            VALUES ({placeholders})
        """

        # Execute the query with the constructed values
        cursor.execute(sql_query, values)
        self.connection.commit()
        

    def get_daily_breakdown_by_timesheetEntryID(self, timesheetEntryID):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()
        search_query = """
            SELECT  [Source]
                ,[FileName]
                ,[TimeSheetEntryID]
                ,[FirstName]
                ,[LastName]
                ,FORMAT(Date, 'MM-dd-yyyy') as Date
                ,[ReportedHours]
                ,[WorkOrderNumber]
                ,[ProjectNumber]
                ,[TaskName]
        FROM [dbo].[vw_EmployeeDetailsAllUnion]
        where TimeSheetEntryID = ?
        """ 
        cursor.execute(search_query,timesheetEntryID)
        columns = [column[0] for column in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return result


    def find_or_create_location(self, location_name, email=None, phone=None, address=None):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()

        # Check if a specific customer ID is provided, include it in the search criteria
        search_query = """
            SELECT LocationID FROM Locations
            WHERE LocationName = ?"""
        
        cursor.execute(search_query, location_name,)
        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            # No existing location found, create a new one
            insert_query = """
                INSERT INTO Locations (LocationName, Email, Phone, Address)
                OUTPUT INSERTED.LocationID
                VALUES (?, ?, ?, ?)
            """
            cursor.execute(insert_query, (location_name, email, phone, address))
            new_location_id = cursor.fetchone()[0]
            self.connection.commit()
            return new_location_id


    def find_or_create_task(self, task_name):
        if not self.connection:
            print("Database connection is not established.")
            return None

        cursor = self.connection.cursor()

        # Check if a specific customer ID is provided, include it in the search criteria
        search_query = """
            SELECT TaskID FROM Tasks
            WHERE TaskName = ?"""
        
        cursor.execute(search_query, task_name,)
        result = cursor.fetchone()

        if result:
            return result[0]
        else:
            # No existing location found, create a new one
            insert_query = """
                INSERT INTO Tasks (TaskName)
                OUTPUT INSERTED.TaskID
                VALUES (?)
            """
            cursor.execute(insert_query, (task_name))
            new_task_id = cursor.fetchone()[0]
            self.connection.commit()
            return new_task_id


    def insert_filename_get_id(self, filename, sheet_name, source_type):
        '''This inserts the filename into the database to 
        be able to determine where the timesheet entry came from.'''
        cursor = self.connection.cursor()
        
        # Check if the filename already exists
        cursor.execute("SELECT FileID FROM ProcessedFiles WHERE FileName = ? AND SheetName = ?", (filename, sheet_name))
        row = cursor.fetchone()
        
        if row:
            # this means file has already been processed. So don't process this file.
            return row, True
        else:
            # Insert new filename
            cursor.execute("INSERT INTO ProcessedFiles (FileName, SheetName, SourceType, DateProcessed) VALUES (?, ?, ?, GETDATE())", (filename,sheet_name,source_type))
            self.connection.commit()
            
            # Retrieve the ID of the newly inserted filename
            cursor.execute("SELECT FileID FROM ProcessedFiles WHERE FileName = ? AND SheetName = ?", (filename, sheet_name))
            new_id = cursor.fetchone()[0]
            self.connection.commit()
            return new_id, False
        

    def delete_data_with_fileID(self, fileID):
        '''This inserts the filename into the database to 
        be able to determine where the timesheet entry came from.'''
        cursor = self.connection.cursor()
        
        # Delete from EmployeeReportedHours
        cursor.execute(f"DELETE FROM EmployeeReportedHours WHERE FileID = '{fileID}'")
        self.connection.commit()
        
        # Delete from CustomerReportedHours
        cursor.execute(f"DELETE FROM CustomerReportedHours WHERE FileID = '{fileID}'")
        self.connection.commit()
        
        # Delete from Internal_Timesheets
        cursor.execute(f"DELETE FROM Internal_Timesheets WHERE FileID = '{fileID}'")
        self.connection.commit()

        # Get the timesheetIDS from the reported expenses, if the file is expense file
        cursor.execute(f"""
            SELECT TimeSheetID
            FROM ReportedExpenses
            WHERE FileID = '{fileID}'
            GROUP BY TimeSheetID
        """)
        timesheet_ids_expenses = cursor.fetchall()
        timesheet_ids_expenses = [row[0] for row in timesheet_ids_expenses]
        
        # Delete from ReportedExpenses
        cursor.execute(f"DELETE FROM ReportedExpenses WHERE FileID = '{fileID}'")
        self.connection.commit()

        # update the TimesheetID to remove the ExpensesUploaded flag
        if timesheet_ids_expenses:
            timesheet_ids_expenses_str = ','.join(map(str, timesheet_ids_expenses))
            cursor.execute(f"""
                UPDATE Timesheets
                SET [ExpensesUploaded] = 0
                WHERE TimeSheetID IN ({timesheet_ids_expenses_str})
            """)
            self.connection.commit()

        timesheet_ids = None # reset timesheet_ids variable

        # Select TimesheetIDs to delete based on conditions, No hours, and no expenses for that timesheet
        cursor.execute("""
            SELECT TimeSheetID
            FROM vw_EmployeeDetailsWeekly
            WHERE ISNULL(InternalReportedHours, 0) = 0
                AND ISNULL(ADPReportedHours, 0) = 0
                AND ISNULL(TotalCustomerReportedHours, 0) = 0
                AND ISNULL([ExpensesUploaded], 0) = 0
        """)
        timesheet_ids = cursor.fetchall()
        timesheet_ids = [row[0] for row in timesheet_ids]

        if timesheet_ids:
            # Create a string of comma-separated TimesheetIDs
            timesheet_ids_str = ','.join(map(str, timesheet_ids))

            # Delete entries from ApprovalTracking that reference the Timesheets to be deleted
            cursor.execute(f"""
                DELETE FROM ApprovalTracking
                WHERE TimesheetID IN ({timesheet_ids_str})
            """)
            self.connection.commit()

            # Delete entries from TimesheetEntries that reference the Timesheets to be deleted
            cursor.execute(f"""
                DELETE FROM TimesheetEntries
                WHERE TimesheetID IN ({timesheet_ids_str})
            """)
            self.connection.commit()

            # Delete the entries from Timesheets
            cursor.execute(f"""
                UPDATE Timesheets
                SET [StatusID] = 7,  [ApprovedHours] = NULL
                WHERE TimeSheetID IN ({timesheet_ids_str})
            """)
            self.connection.commit()

            cursor.execute(f"""
                DELETE FROM Timesheets
                WHERE TimeSheetID IN ({timesheet_ids_str})
            """)
            self.connection.commit()

        # Delete File entry in ProcessedFiles table
        cursor.execute(f"DELETE FROM ProcessedFiles WHERE FileID = '{fileID}'")
        self.connection.commit()


    def get_processed_files(self, source_type=None):
        cursor = self.connection.cursor()
        
        # Check if the filename already exists
        cursor.execute("""SELECT [FileID]
                                ,[FileName]
                                ,[SheetName]
                                ,[FileHash]
                                ,[SourceType]
                                ,[DateProcessed]
            FROM [dbo].[ProcessedFiles]""")        
        columns = [column[0] for column in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return result
    
    def get_rate_types(self):
        """
        This function returns the ID and RateType from the RateTypes table.
        """
        cursor = self.connection.cursor()
        cursor.execute("SELECT ID, RateType FROM RateTypes")
        columns = [column[0] for column in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return result

    def execute_many(self, query, parameters_list):
        """
        Execute a query multiple times with different parameters.
        
        Args:
            query (str): The SQL query to execute
            parameters_list (list): List of parameter tuples to execute with the query
        """
        if not self.connection:
            print("Not connected to a database")
            return None
            
        cursor = self.connection.cursor()
        try:
            cursor.executemany(query, parameters_list)
            self.connection.commit()
            return True
        except Exception as e:
            airtanker_app.logger.error(f"Failed to execute batch query: {e}")
            return None

    def batch_insert_employee_reported_hours(self, batch_data):
        """
        Insert multiple employee reported hours records in a single batch operation.

        Args:
            batch_data (list): List of tuples containing the data to insert
                Each tuple should contain: (timesheet_entry_id, date, customer_id, project_id, task_id,
                                          employee_reported_hours, notes, file_id, rate_type_id,
                                          work_order_id, employee_id, location_id)
                Note: location_id can be None

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.connection:
            airtanker_app.logger.error("Database connection is not established for batch_insert_employee_reported_hours.")
            return False

        cursor = self.connection.cursor()
        try:
            # Define the SQL query with all possible columns
            query = """
                INSERT INTO [dbo].[EmployeeReportedHours]
                    ([TimesheetEntryID], [Date], [CustomerID], [ProjectID], [TaskID],
                     [EmployeeReportedHours], [Notes], [FileID], [RateTypeID],
                     [WorkOrderID], [EmployeeID], [LocationID])
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            # Execute the batch insert
            cursor.executemany(query, batch_data)
            self.connection.commit()
            return True
        except Exception as e:
            airtanker_app.logger.error(f"Error in batch_insert_employee_reported_hours: {e}", exc_info=True)
            try:
                self.connection.rollback()
            except Exception as rb_ex:
                airtanker_app.logger.error(f"Error during rollback: {rb_ex}", exc_info=True)
            return False
        finally:
            if cursor:
                cursor.close()

def get_week_ending_date(date):
    # Calculate how many days to add to get to Sunday
    # date.weekday() returns 0 for Monday, ..., 6 for Sunday
    days_until_sunday = 6 - date.weekday()  # Since Sunday is 6
    week_ending_date = date + timedelta(days=days_until_sunday)
    return week_ending_date


def get_first_match(arr, key, search_item):
    for item in arr:
        if item.get(key) == search_item:
            return item
    return None  # Return None if no match is found
