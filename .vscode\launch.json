{"configurations": [{"name": "Python Debugger: Flask", "type": "debugpy", "request": "launch", "module": "flask", "env": {"FLASK_APP": "${workspaceFolder}/airtanker/app.py", "FLASK_DEBUG": "1"}, "args": ["run", "--no-debugger", "--no-reload", "--host=0.0.0.0"], "jinja": true, "autoStartBrowser": false}, {"name": "Python: <PERSON><PERSON> Attach", "type": "debugpy", "request": "attach", "justMyCode": false, "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}/airtanker", "remoteRoot": "/home/<USER>"}]}, {"name": "Attach to Chrome", "port": 5001, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}/airtanker"}, {"name": "Docker: Python - Flask", "type": "docker", "request": "launch", "preLaunchTask": "docker-run: debug", "python": {"pathMappings": [{"localRoot": "${workspaceFolder}/airtanker/", "remoteRoot": "/home/<USER>"}], "projectType": "flask"}}, {"name": "Python: Remote Docker Debug", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}/airtanker", "remoteRoot": "/home/<USER>"}], "justMyCode": false}]}