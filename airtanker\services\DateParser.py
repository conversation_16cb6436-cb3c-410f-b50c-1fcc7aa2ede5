from datetime import datetime, timedelta, date
import csv
from io import StringIO
import re


def can_parse_date(string):
    string = str(string)  # Ensure the input is treated as a string
    for date_format in date_formats:
        try:
            datetime.strptime(string, date_format)
            return True
        except ValueError:
            continue
    return False


def get_date_exception(string):
    string = str(string)  # Ensure the input is treated as a string
    for date_format in date_formats:
        try:
            date = datetime.strptime(string, date_format)
            return date
        except ValueError:
            continue
    raise Exception("Date not found in cell.") 


def find_date_column_excel(sheet):
    column_scores = {}
    for col_idx, col in enumerate(sheet.iter_cols(min_row=0, max_row=50, values_only=True)):       
        score = 0
        for cell in col:            
            score += can_parse_date(cell)
        column_scores[col_idx] = score
    best_guess_index = max(column_scores, key=column_scores.get) if column_scores else None
    return best_guess_index


# Define function to check for date-like patterns to exclude
def is_date_string(string):
    return bool(re.match(r'\d{4}-\d{2}-\d{2}', string)) or bool(re.match(r'\d{2}/\d{2}/\d{4}', string))


def find_date_column_in_csv(csv_data):
    reader = csv.reader(StringIO(csv_data))
    headers = next(reader)  # Assuming the first row contains headers
    
    # Initialize a dictionary to score each column
    column_scores = {i: 0 for i in range(len(headers))}
    
    for row in reader:
        for i, cell in enumerate(row):
            if can_parse_date(cell):
                column_scores[i] += 1
    
    # Find the column index with the highest score
    best_guess_index = max(column_scores, key=column_scores.get)
    return best_guess_index, headers[best_guess_index]


def check_daterange(start_date, end_date):
    range_length = (parse_date(end_date) - parse_date(start_date)).days
    return range_length < 7


def parse_date(date_str):
    return datetime.strptime(date_str, "%m/%d/%Y")


date_formats = [
    "%m/%d/%Y",  # 01/31/2020
    "%m-%d-%Y",  # 01-31-2020
    "%Y/%m/%d",  # 2020/01/31
    "%Y-%m-%d",  # 2020-01-31
    "%d/%m/%Y",  # 31/01/2020
    "%d-%m-%Y",  # 31-01-2020
    "%d %b %Y",  # 01 Jan 2024
    '%Y-%m-%d %H:%M:%S' # '2023-11-13 00:00:00'
    # Add more formats as needed
]


def get_nth_weekday_of_month(year, month, weekday, n):
    """Returns the date of the nth weekday of the given month and year. `n` can be negative to count from the end of the month."""
    if n > 0:
        # Finding the nth weekday from the start of the month
        count = 0
        d = date(year, month, 1)
        while count < n:
            if d.weekday() == weekday:
                count += 1
            if count == n:
                return d
            d += timedelta(days=1)
    else:
        # Finding the nth weekday from the end of the month
        # Get the last day of the month
        if month == 12:
            last_day = date(year, 12, 31)
        else:
            last_day = date(year, month + 1, 1) - timedelta(days=1)

        count = 0
        d = last_day
        while count < -n:
            if d.weekday() == weekday:
                count += 1
            if count == -n:
                return d
            d -= timedelta(days=1)

def get_thanksgiving(year):
    """ Returns the date of Thanksgiving for the given year. """
    return get_nth_weekday_of_month(year, 11, 3, 4)  # 3 is for Thursday

def get_memorial_day(year):
    """ Returns the date of Memorial Day for the given year. """
    return get_nth_weekday_of_month(year, 5, 0, -1)  # 0 is for Monday, -1 for last occurrence

def get_labor_day(year):
    """ Returns the date of Labor Day for the given year. """
    return get_nth_weekday_of_month(year, 9, 0, 1)  # 0 is for Monday

def check_holiday(d):
    year = d.year
    # Calculate dynamic holidays
    memorial_day = get_memorial_day(year)
    labor_day = get_labor_day(year)
    thanksgiving = get_thanksgiving(year)

    # Define the static and dynamic holidays
    holidays = {
        date(year, 1, 1): "New Years Day",
        memorial_day: "Memorial Day",
        date(year, 7, 4): "Independence Day",
        labor_day: "Labor Day",
        thanksgiving: "Thanksgiving Day",
        #date(year, 12, 24): "Christmas Eve",
        date(year, 12, 25): "Christmas"
        #date(year, 12, 31): "New Years Eve"
    }

    # Add the day after Thanksgiving as a floating holiday
    holidays[thanksgiving + timedelta(days=1)] = "Floating Holiday"
    # Add the floating holidays associated with Christmas
    holidays[date(year, 12, 26)] = "Floating Holiday"
    if date(year, 12, 31).weekday() == 5:  # If New Year's Eve is Saturday
        holidays[date(year, 12, 30)] = "Floating Holiday"  # Friday, 30th is a holiday
    elif date(year, 12, 31).weekday() == 6:  # If New Year's Eve is Sunday
        holidays[date(year, 12, 29)] = "Floating Holiday"  # Friday, 29th is a holiday

    # Check for observed holidays
    if d.weekday() == 0 and (d - timedelta(days=1)) in holidays:  # Monday check
        return True
    elif d.weekday() == 4 and (d + timedelta(days=1)) in holidays:  # Friday check
        return True

    # Check if the date itself is a holiday
    return d in holidays

# # Example usage:
# your_date = date(2024, 9, 2)  # Labor Day 2024
# is_holiday = check_holiday(your_date)
# print(f"{your_date} is a holiday: {is_holiday}")
