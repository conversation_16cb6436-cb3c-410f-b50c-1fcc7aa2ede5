import os

PRODUCTION = os.getenv("PRODUCTION", "false").lower() == "true"

if PRODUCTION:
    import eventlet
    eventlet.monkey_patch()

from main import airtanker_app
import logging
from endpoints import logins, exports, progress, tasks, api, timesheet_wizard, decorators, error_handling, expense_wizard, pdf_creation, dashboard

logging.getLogger('camelot').setLevel(logging.CRITICAL)
logging.basicConfig(
    filename='airtanker.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

if __name__ == '__main__':
    if not PRODUCTION:
        print("🐞 Development mode")
        airtanker_app.run(host='0.0.0.0', port=5000, debug=True) # Dissable use_reloader if debugging with VS Code
        print("Not Production")
    else:
        print("🚀 Production mode")