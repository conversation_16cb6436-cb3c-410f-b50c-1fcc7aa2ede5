LDAPADDRESS="ldap://***********:389"

DB_SERVER="***********"
DB_DATABASE="AirTanker-DEV"
DB_USERNAME="sa"
DB_PASSWORD="Puritan7-Disposal-Sudoku"

ODOO_URL="https://atom-erp-staging-21441344.dev.odoo.com/"
ODOO_DB="atom-erp-staging-21441344"

ENABLE_NAME_MATCHING=false

DEBUG_BYPASS_LOGIN=False
PRODUCTION=False

DETAILED_LOGGING=True  # or false for simple logging

ODOO_FIELDS=['name', 'project_id', 'x_studio_plant_name', 'x_studio_site_sheet', 'x_studio_sow_partner', 'stage_id', 'partner_id', 'x_studio_start_date', 'x_studio_employee_type', 'x_sow_overtime_rule', 'x_sow_double_time_rule', 'x_inv_overtime_rule', 'x_inv_double_time_rule', 'x_studio_end_date', 'x_studio_assigned_to', 'x_studio_job_position_1', 'x_sow_rate', 'x_sow_ot_rate', 'x_sow_dt_rate', 'x_sow_ht_rate', 'x_sow_tt_rate', 'x_studio_sow_company', 'x_sow_rate_type', 'x_studio_approved_travel_hours', 'x_inv_rate', 'x_inv_ot_rate', 'x_inv_dt_rate', 'project_analytic_account_id', 'project_sale_order_id']

# Odoo Fields specific - PRD
STAGE_ID_FIELD='stage_id'
EMP_INFO_FIELD='x_studio_assigned_to'
PROJECT_INFO_FIELD='project_id'
CUSTOMER_INFO_FIELD='partner_id'
WO_ID_FIELD='id'
WO_NUMBER_FIELD='name'
START_DATE_FIELD='x_studio_start_date'
END_DATE_FIELD='x_studio_end_date'
SOW_CONTACT='x_studio_contact'

PAYCOR_BASE_URL = "https://apis.paycor.com"
PAYCOR_APIM_KEY = '019c32a777104f15aa7c7d68d0df8802'
PAYCOR_ENTITY_ID = 179457 # Atom Design Services LLC
PAYCOR_CLIENT_ID = '1608ea08dd83cd5e5e71'
PAYCOR_CLIENT_SECRET = '12z8yDqefhgXxFdxT1MKunwE9Xu1NDSqx967Cn6N8YM'
PAYCOR_ACCESS_TOKEN='eyJhbGciOiJSUzI1NiIsImtpZCI6ImFlYWVmYTU1Njg0Y2RmMDBkNjBjNmZhYTRjZmRmYjVkIiwidHlwIjoiSldUIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BEhjDUAqjs8iy8cM74tSISuw-m3WJAR6wOcgcxLxYHynGRonoVq--zg2D1yG9stxuRmyepMFr_WLBDV_iLWqCnoPdPVimUcnOcjif1ilBbHJqnoeesCFrWxeWL8LPkXK9pD2Ajc1PhN0j7qK7qUJUqKoutmeAfgueV284ftFxltghlKuTSDpKLZepXwJ08dzHYVg77pOcFcVU5sYZrY1y0JTc4z9fh_ZXXy720w-MrG05Nrcxkb0dYWCEktB0FV9UtYrUVo86poVZlng_9mjFi04njVV8rrsqSJC0OR_Ae_cb5lxwfLxVp22UGLLQHaGw2vo_u0VtDog7NVVr-iySw'
PAYCOR_REFRESH_TOKEN='ee0fa75475ad91ff32762bd4d3afedf1b81536b228546f115f685192f4435d76'