
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">
    
    <title>AirTanker</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_customer.css') }}"  />
    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
            margin-left: 10px;
            margin-right: 10px;
        }
        /* Style the console-like area */
        #console {
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }
        /* Style different types of messages */
        .error {
            color: red;
        }
        #detailsModal .modal-dialog {
            max-width: 90%;
        }
        /* Add a horizontal scrollbar to the modal body */
        #detailsModal .modal-body {
            overflow-x: auto;
        }
        .file-upload-wrapper:hover {
            background-color: #f3f4f6;
        }
        .file-upload-wrapper i {
            color: #0f57f3;            
        }
        .list-group-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-left: 10px;
            margin-right: 10px;
            margin-bottom: 1px;
        }
        .list-group-item i.fa-times {
            color: red;
            cursor: pointer;
        }
        #browse-btn {
            color: blue; /* Set the text color */
            text-decoration: underline; /* Underline the text to mimic a hyperlink */
            cursor: pointer; /* Change the cursor to indicate it's clickable */
        }
        .progress-container {
            width: 80%;
            background-color: #e0e0e0;
            position: fixed; /* Stick to the top */
            z-index: 1000;
            top: 50;
            left: 0;
            right: 0; /* Added to work with margin: auto; */
            margin: auto; /* Center the element */
        }

        
        .progress-bar {
            height: 7px;
            background-color: #4CAF50;
            width: 42%; /* Start state */
            animation-name: loadProgress;
            animation-duration: 2s; /* Customize this value */
            animation-fill-mode: forwards; /* Keeps the state at 66% after animation */
        }
        @keyframes loadProgress {
            from {
                width: 42%;
            }
            to {
                width: 87%;
            }
        }
        div#loading {
            width: 500px;
            height: 500px;
            display: none;
            background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
            background-size: contain;
            cursor: wait;
            z-index: 1000;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            
            box-shadow: none; /* Ensure no shadow is applied */
            filter: none; /* Remove any filters that might create a shadow effect */
        }
    </style>
</head>
<body class="bg-gradient-white">
    <!-- Modal for the spinner overlay -->
    <div class="modal" id="spinnerModal" tabindex="-1" role="dialog" aria-labelledby="spinnerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Processing...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    <div id="progressLabel" class="fadeIn" style="display: block; z-index: 1000;text-align: center; position: fixed; width: 100%; top: 20px;">
        Step 3: Upload Customer Provided TimeSheets
    </div>

    <!-- The file upload areas -->

    <div id="loading"></div>
    <div class="wrapper fadeIn" id="content">
        <!-- <img style="margin-bottom: 10;" src="{{ url_for('static', filename='assets/custom_pic.webp') }}" width="200" alt=""> -->
        <div id="formContent" enctype="multipart/form-data">
            <h1 class="h4 text-gray-900 mb-4 fadeIn first" style="margin-top: 5%;">                                            
                Upload Customer Timesheets
            </h1>
            <div class="file-upload-wrapper" onclick="document.getElementById('file-upload').click()">
                <i class="fas fa-cloud-upload-alt fa-2x"></i>
                <p>Drag files here or <a href="#" onclick="handleBrowseClick(event)">Browse</a></p>
            </div>
            <input id="file-upload" type="file" name="uploaded_files[]" multiple style="display: none;" accept=".xlsx, .xlsm, .pdf" onchange="addFiles()">
            <ul class="list-group mt-3" id="file-list" style=" margin-bottom: 20px;">
                <!-- Files will be listed here -->
            </ul>
            <input id="process-files-btn" type="submit" style="display: none;" onclick="processFiles();" value="Process Files">
            <div id="formFooter">
                <a class="underlineHover fadeIn third" href="/">Cancel</a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <script type="text/javascript">
        // <![CDATA[
            function loading(){
                $("#loading").show();
                $("#content").hide();       
            }
        // ]]>
    </script>
    <script>        
    let selectedFiles = [];

    function showProgress(){
        $('#spinnerModal').modal({
        backdrop: 'static', // Prevent clicking outside the modal to close
            keyboard: false // Prevent closing the modal with keyboard ESC key
        });
    }


    function addFiles() {
        var files = document.getElementById('file-upload').files;
        for (var i = 0; i < files.length; i++) {
            selectedFiles.push(files[i]);
        }
        updateFileList();
        document.getElementById('file-upload').value = ''; // Clear the current selection
    }

    function updateFileList() {
        var output = document.getElementById('file-list');
        output.innerHTML = '';
        for (var i = 0; i < selectedFiles.length; ++i) {
            output.innerHTML += '<li class="list-group-item">' +
                                '<i class="fas fa-file-alt"></i> ' +
                                selectedFiles[i].name +
                                '<i class="fas fa-times" onclick="removeFile(' + i + ')"></i>' +
                                '</li>';
        }
        updateVisibilityOfProcessButton();
    }

    function removeFile(index) {
        selectedFiles.splice(index, 1); // Remove the file from the array
        updateFileList(); // Update the list
    }
        



    // Show compare button
    function updateVisibilityOfProcessButton() {
        // Check if both arrays have at least one file
        if (selectedFiles.length > 0 && selectedFiles.length > 0) {
            document.getElementById('process-files-btn').style.display = 'block'; // Show the button
        } else {
            document.getElementById('process-files-btn').style.display = 'none'; // Hide the button
        }
    }

    // Compare button click
    function processFiles() {
        $("#loading").show();
        $("#content").hide();  
        const formData = new FormData()

        selectedFiles.forEach(file=> {
            formData.append(`uploaded_files[]`, file);
        });

        fetch('/upload_customer_timesheets', {
            method: 'POST',
            headers: {
            },
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
        if (!response.ok) {
            // If the server response is not ok (e.g., 500 Internal Server Error), throw an error
            throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        return response.json();
        })
        .then(data => {
            if (data.redirect_url) {
                // Store errors in sessionStorage or localStorage
                sessionStorage.setItem('errors', JSON.stringify(data.errors));
                sessionStorage.setItem('name_errors', JSON.stringify(data.name_errors));
                // Redirect
                window.location.href = data.redirect_url;
            }
            else if (data.completion_html){
                // Render Completion template
                document.body.innerHTML = data.completion_html;
                // Redirect after 5 seconds
                setTimeout(function() {
                    window.location.href = '/approvals/timesheets'; // Replace '/approvals' with your desired endpoint
                }, 2500); // 5000 milliseconds = 5 seconds
            }
        })
        .catch(error => {
            // Handle any errors that occurred during the fetch or due to a server error
            console.error('Error:', error);
            alert('An error occurred while processing the files. Please try again.');
            $("#loading").hide();
            $("#content").show();
        })
        .finally(() => {
            // This will always execute regardless of whether the fetch was successful or not
        });
    };


    function handleBrowseClick(event) {
        event.stopPropagation(); // Stop event propagation to prevent double triggering
        document.getElementById('file-upload').click();
    }

    document.addEventListener("DOMContentLoaded", (event) => {
        setTimeout(() => {
            document.getElementById('progressLabel').style.display = 'block';
        }, 2000); // Match this duration to your CSS animation-duration
    });

    </script>
</body>
