# Introduction 

## Purpose
Our web application is designed to streamline the reconciliation of hours reported by employees/contractors and customers, facilitating the swift creation of invoices, vendor bills, and other financial documents. By automating these processes and integrating them directly with our ERP system through an API, the application enhances administrative efficiency and reduces potential errors associated with manual entry.

## Target Audience
The primary users are our internal administration team, responsible for managing and processing timesheet data and financial documentation.

## Key Features
- **Data Parsing**: Extracts and processes data from employee and customer timesheets.
- **Database Integration**: Manages data in a SQL Server database.
- **ERP System Integration**: Uses an API to retrieve work orders, and input invoices, bills, and expenses directly into the ERP system.
- **Task Progress Management**: Employs a Redis server running under Docker to manage and display backend task progress in real-time.
- **User Authentication**: Supports secure logins through integration with an LDAP server.

## Technology Stack
- **Backend**: Python with Flask.
- **Frontend**: JavaScript and HTML.
- **Server Management**: Gunicorn and NGINX.
- **API Integration**: Odoo API.
- **Task Management**: Redis within Docker containers.

## Deployment and Configuration
- **CI/CD Pipelines**: Utilizes Azure DevOps for continuous integration and deployment. Code updates are automatically pushed to the development server upon changes to the development branch and to the production server upon changes to the main branch.
- **Configuration Management**: Uses a .env file to securely manage database credentials, LDAP server details, and API endpoint configurations.

## Benefits
This application simplifies administrative processes by automating the validation and exportation of timesheet data into financial documents, significantly reducing manual data entry and increasing accuracy. The use of Azure DevOps and .env files ensures that updates and configurations are managed securely and efficiently.

# Deployment
AirTanker utilizes the Docker framework - specifically docker compose - to orchestrate the various dependent services required for the AirTanker web application. Both the AirTanker app and the NGINX app are built from base images and have customizations applied via a DockerFile in their respective directories. This sets the environment up properly for the AirTanker web app. The Redis image is a direct reference to the official, and listed in the compose.yaml file.

The process for deploying the docker environment is as follows:

1.	Ensure that docker is installed on the target system, including the docker compose plugin. The method is out of the scope of this document, but you can find instructions from docker here: https://docs.docker.com/compose/install/

    Portainer is an exceptionally handy tool to install as well. It offers convenient tools for managing container "stacks" via docker compose and insight into operations without using command-line tools. Follow the installation guide here, if applicable: https://docs.portainer.io/start/install-ce

2.	Copy the root airtanker folder (containing airtanker & nginx subfolders) to the target system. If the target system is linux, it is preferred to copy the files to the /home/<USER>
    
    Make sure to navigate to the root airtanker folder in the target system command-line. In linux, it would be ```cd airtanker``` (assuming you placed it the home directory, and the shell is currently targeting the home directory). In Docker Desktop, launch the terminal on the bottom-right corner of the screen and navigate to the airtanker folder.

    In Portainer, It is advisable to utilize the "Add Stack" interface, and point to the Azure DevOps repository to grab files. One important note - you will need to copy the .env file from your development environment to the container manually. DevOps does not - and will not - replicate the .env file.
    ![Portainer Screenshot](portainer-ss.png)

3.	Run the command ```docker compose up -d```. On a Portainer environment, simply pull and deploy/start the stack via the interface.

4.  As mentioned in a portainer environment, you need to copy the .env file manually from your development environment. To do so, make sure it is copied to the target (host) system, then run the docker copy command into the container: https://docs.docker.com/reference/cli/docker/container/cp/
